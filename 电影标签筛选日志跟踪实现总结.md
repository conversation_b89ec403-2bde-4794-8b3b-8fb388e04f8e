# 电影标签筛选日志跟踪实现总结

## 项目需求回顾

根据项目要求，需要为电影分类标签的筛选功能添加完整的VOD_FLOW日志跟踪，包括：

1. 点击电影分类标签显示该标签下的电影资源
2. 再次点击标签弹出搜索条件
3. 用户根据搜索条件进行筛选
4. 展示筛选后的电影内容
5. 使用统一的VOD_FLOW ID进行全流程跟踪

## 实现方案

### 1. 日志系统扩展 (FlowLogger.java)

#### 新增日志阶段常量
```java
// 电影标签筛选流程相关
public static final String MOVIE_TAG_CLICK = "MOVIE_TAG_CLICK";
public static final String MOVIE_TAG_FILTER_SHOW = "MOVIE_TAG_FILTER_SHOW";
public static final String MOVIE_TAG_FILTER_HIDE = "MOVIE_TAG_FILTER_HIDE";
public static final String MOVIE_TAG_FILTER_SELECT = "MOVIE_TAG_FILTER_SELECT";
public static final String MOVIE_TAG_FILTER_APPLY = "MOVIE_TAG_FILTER_APPLY";
public static final String MOVIE_TAG_FILTER_REQUEST = "MOVIE_TAG_FILTER_REQUEST";
public static final String MOVIE_TAG_FILTER_RESULT = "MOVIE_TAG_FILTER_RESULT";
```

#### 新增专门的日志方法
- `logMovieTagClick()` - 记录电影标签点击
- `logMovieTagFilterShow()` - 记录筛选面板显示
- `logMovieTagFilterHide()` - 记录筛选面板隐藏
- `logMovieTagFilterSelect()` - 记录筛选条件选择
- `logMovieTagFilterApply()` - 记录筛选条件应用
- `logMovieTagFilterRequest()` - 记录筛选请求发送
- `logMovieTagFilterResult()` - 记录筛选结果返回

### 2. VodActivity 修改

#### 添加VOD_FLOW ID管理
```java
private String mFlowId; // VOD_FLOW ID for movie tag filtering

@Override
protected void initView() {
    // 初始化VOD_FLOW ID
    mFlowId = "MOVIE_TAG_" + System.currentTimeMillis() % 100000;
    // ...
}

public String getFlowId() {
    return mFlowId;
}
```

#### 标签点击日志跟踪
```java
@Override
public void onItemClick(Class item) {
    // 记录电影标签点击日志
    FlowLogger.logMovieTagClick(mFlowId, item.getTypeName(), item.getTypeId(), 
            getKey(), item.getFilter() != null);
    updateFilter(item);
}
```

#### 筛选面板显示/隐藏日志跟踪
```java
private void updateFilter(Class item) {
    if (item.getFilter() == null) return;
    
    boolean willShow = item.toggleFilter();
    
    // 记录筛选面板显示/隐藏日志
    if (mFlowId != null) {
        if (willShow) {
            FlowLogger.logMovieTagFilterShow(mFlowId, item.getTypeName(), item.getFilters().size());
        } else {
            FlowLogger.logMovieTagFilterHide(mFlowId, item.getTypeName());
        }
    }
    
    getFragment().toggleFilter(willShow);
    mAdapter.notifyArrayItemRangeChanged(0, mAdapter.size());
}
```

#### 筛选条件应用日志跟踪
```java
@Override
public void onRefresh(Class item) {
    // 记录筛选条件刷新日志（再次点击标签）
    if (mFlowId != null) {
        FlowLogger.logMovieTagFilterApply(mFlowId, item.getTypeName(), item.getTypeId(), 
                getFragment().getActiveFilterCount());
    }
    getFragment().onRefresh();
}
```

### 3. VodFragment 修改

#### 添加辅助方法
```java
public int getActiveFilterCount() {
    return mExtends.size();
}

private String getFlowIdFromActivity() {
    if (getActivity() instanceof com.fongmi.android.tv.ui.activity.VodActivity) {
        return ((com.fongmi.android.tv.ui.activity.VodActivity) getActivity()).getFlowId();
    }
    return null;
}

private String getCurrentTagName() {
    return "电影标签_" + getTypeId();
}
```

#### 筛选条件选择日志跟踪
```java
private void setClick(ArrayObjectAdapter adapter, String key, Value item) {
    for (int i = 0; i < adapter.size(); i++)
        ((Value) adapter.get(i)).setActivated(item);
    adapter.notifyArrayItemRangeChanged(0, adapter.size());
    
    // 记录筛选条件选择日志
    String flowId = getFlowIdFromActivity();
    if (flowId != null) {
        FlowLogger.logMovieTagFilterSelect(flowId, getCurrentTagName(), key, item.getV(), item.getN());
    }
    
    if (item.isActivated())
        mExtends.put(key, item.getV());
    else
        mExtends.remove(key);
    onRefresh();
}
```

#### FlowID传递
```java
public void onRefresh() {
    // 传递FlowId给SiteViewModel
    String flowId = getFlowIdFromActivity();
    if (flowId != null) {
        mViewModel.setFlowId(flowId);
    }
    getVideo();
}

private void getVideo(String typeId, String page) {
    // ...
    // 传递FlowId给SiteViewModel
    String flowId = getFlowIdFromActivity();
    if (flowId != null) {
        mViewModel.setFlowId(flowId);
    }
    
    mViewModel.categoryContent(getKey(), typeId, page, true, mExtends);
}
```

### 4. SiteViewModel 修改

#### 筛选请求和结果日志跟踪
```java
public void categoryContent(String key, String tid, String page, boolean filter, HashMap<String, String> extend) {
    execute(result, () -> {
        long startTime = System.currentTimeMillis();
        Site site = VodConfig.get().getSite(key);
        
        // 记录筛选请求发送日志
        String filterParams = extend.isEmpty() ? "无" : App.gson().toJson(extend);
        if (currentFlowId != null && !extend.isEmpty()) {
            FlowLogger.logMovieTagFilterRequest(currentFlowId, "分类_" + tid, tid, key, page, filterParams);
        }
        
        // ... 执行请求逻辑 ...
        
        // 记录筛选结果返回日志
        long duration = System.currentTimeMillis() - startTime;
        if (currentFlowId != null && !extend.isEmpty()) {
            int resultCount = result != null && result.getList() != null ? result.getList().size() : 0;
            FlowLogger.logMovieTagFilterResult(currentFlowId, "分类_" + tid, tid, resultCount, duration, result != null);
        }
        
        return result;
    });
}
```

## 实现特点

### 1. 统一的VOD_FLOW ID
- 在VodActivity初始化时生成唯一的FlowID
- 格式：`MOVIE_TAG_xxxxx`
- 整个筛选流程使用同一个FlowID

### 2. 完整的流程覆盖
- 标签点击 → 筛选面板显示 → 筛选条件选择 → 筛选应用 → 请求发送 → 结果返回
- 每个关键节点都有对应的日志记录

### 3. 丰富的上下文信息
- 标签名称和ID
- 站点信息
- 筛选条件详情
- 结果数量和耗时
- 成功/失败状态

### 4. 错误处理
- 支持异常情况的日志记录
- 提供带错误信息的日志方法重载

## 日志输出示例

```
[12:34:56.789] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_CLICK] 点击电影标签: 热门电影 (ID:1) 站点:某站点 有筛选:是
[12:34:56.790] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_FILTER_SHOW] 显示筛选面板: 热门电影 筛选条件数:3
[12:34:58.123] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_FILTER_SELECT] 选择筛选条件: 热门电影 year=2023 (2023年)
[12:34:58.124] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_FILTER_APPLY] 应用筛选条件: 热门电影 (ID:1) 激活筛选数:1
[12:34:58.125] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_FILTER_REQUEST] 发送筛选请求: 分类_1 (ID:1) 站点:某站点 页码:1 筛选参数:{"year":"2023"}
[12:34:59.456] [FlowID:MOVIE_TAG_12345] [MOVIE_TAG_FILTER_RESULT] 筛选结果返回: 分类_1 (ID:1) 成功 结果数:25 耗时:1331ms
```

## 技术优势

1. **可追溯性**：通过统一的FlowID可以完整追踪用户的筛选操作流程
2. **可诊断性**：详细的日志信息有助于快速定位问题
3. **可分析性**：丰富的上下文信息支持用户行为分析
4. **可扩展性**：日志系统设计支持后续功能扩展
5. **性能监控**：记录请求耗时，支持性能分析

## 后续建议

1. **日志聚合**：考虑将日志发送到中央日志系统进行聚合分析
2. **性能优化**：对于高频操作，可以考虑异步日志记录
3. **用户隐私**：确保日志记录符合隐私保护要求
4. **日志清理**：制定日志文件的清理策略，避免占用过多存储空间

## 总结

本次实现成功为电影标签筛选功能添加了完整的VOD_FLOW日志跟踪系统，实现了从用户点击标签到展示筛选结果的全流程监控。通过统一的FlowID管理和详细的日志记录，大大提升了系统的可观测性和问题诊断能力。
