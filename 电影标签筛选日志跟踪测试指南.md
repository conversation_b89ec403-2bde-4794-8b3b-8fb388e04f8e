# 电影标签筛选日志跟踪测试指南

## 功能概述

本次实现为电影分类标签的筛选功能添加了完整的VOD_FLOW日志跟踪系统，能够跟踪从点击标签到展示筛选结果的整个流程。

## 实现的功能

### 1. 日志阶段定义
在 `FlowLogger.java` 中添加了以下日志阶段：
- `MOVIE_TAG_CLICK` - 电影标签点击
- `MOVIE_TAG_FILTER_SHOW` - 筛选面板显示
- `MOVIE_TAG_FILTER_HIDE` - 筛选面板隐藏
- `MOVIE_TAG_FILTER_SELECT` - 筛选条件选择
- `MOVIE_TAG_FILTER_APPLY` - 筛选条件应用
- `MOVIE_TAG_FILTER_REQUEST` - 筛选请求发送
- `MOVIE_TAG_FILTER_RESULT` - 筛选结果返回

### 2. 日志方法实现
添加了以下专门的日志方法：
- `logMovieTagClick()` - 记录标签点击
- `logMovieTagFilterShow()` - 记录筛选面板显示
- `logMovieTagFilterHide()` - 记录筛选面板隐藏
- `logMovieTagFilterSelect()` - 记录筛选条件选择
- `logMovieTagFilterApply()` - 记录筛选条件应用
- `logMovieTagFilterRequest()` - 记录筛选请求
- `logMovieTagFilterResult()` - 记录筛选结果

### 3. VOD_FLOW ID管理
- 在 `VodActivity` 中统一管理VOD_FLOW ID
- 确保整个筛选流程使用同一个FlowID
- 通过 `getFlowId()` 方法向Fragment传递FlowID

### 4. 关键节点日志跟踪
- **VodActivity.onItemClick()** - 标签点击日志
- **VodActivity.updateFilter()** - 筛选面板显示/隐藏日志
- **VodActivity.onRefresh()** - 筛选条件应用日志
- **VodFragment.setClick()** - 筛选条件选择日志
- **SiteViewModel.categoryContent()** - 筛选请求和结果日志

## 测试流程

### 1. 准备测试环境
1. 确保应用已正确编译
2. 启用日志输出（确保VOD_FLOW标签的日志可见）
3. 准备一个有筛选条件的电影分类

### 2. 测试步骤

#### 步骤1：首次点击电影标签
**操作**：点击一个电影分类标签（如"热门电影"）
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_CLICK] 点击电影标签: 热门电影 (ID:1) 站点:xxx 有筛选:是
```

#### 步骤2：筛选面板显示
**操作**：如果标签有筛选条件，面板会自动显示
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_FILTER_SHOW] 显示筛选面板: 热门电影 筛选条件数:3
```

#### 步骤3：选择筛选条件
**操作**：点击筛选条件中的某个选项（如选择"2023年"）
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_FILTER_SELECT] 选择筛选条件: 热门电影 year=2023 (2023年)
```

#### 步骤4：应用筛选条件
**操作**：再次点击标签或触发刷新
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_FILTER_APPLY] 应用筛选条件: 热门电影 (ID:1) 激活筛选数:1
```

#### 步骤5：发送筛选请求
**操作**：系统自动发送带筛选条件的请求
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_FILTER_REQUEST] 发送筛选请求: 分类_1 (ID:1) 站点:xxx 页码:1 筛选参数:{"year":"2023"}
```

#### 步骤6：接收筛选结果
**操作**：系统接收并处理筛选结果
**预期日志**：
```
[时间] [FlowID:MOVIE_TAG_xxxxx] [MOVIE_TAG_FILTER_RESULT] 筛选结果返回: 分类_1 (ID:1) 成功 结果数:25 耗时:1200ms
```

### 3. 验证要点

#### 3.1 FlowID一致性
- 确保整个流程中所有日志都使用相同的FlowID
- FlowID格式应为 `MOVIE_TAG_xxxxx`

#### 3.2 日志完整性
- 每个关键步骤都应有对应的日志记录
- 日志信息应包含必要的上下文信息（标签名、ID、站点等）

#### 3.3 时序正确性
- 日志的时间戳应该反映操作的正确顺序
- 筛选请求应在筛选条件选择之后
- 筛选结果应在筛选请求之后

#### 3.4 错误处理
- 测试网络错误情况下的日志记录
- 验证异常情况下的错误日志

## 日志查看方法

### Android Studio Logcat
1. 打开Android Studio的Logcat窗口
2. 设置过滤器：标签为 `VOD_FLOW`
3. 运行测试流程，观察日志输出

### ADB命令行
```bash
adb logcat -s VOD_FLOW
```

### 日志文件
如果应用配置了日志文件输出，可以查看对应的日志文件。

## 故障排除

### 1. 没有日志输出
- 检查日志级别设置
- 确认VOD_FLOW标签没有被过滤
- 验证FlowLogger的导入和调用

### 2. FlowID不一致
- 检查VodActivity的FlowID初始化
- 验证Fragment获取FlowID的方法
- 确认SiteViewModel的FlowID设置

### 3. 日志信息不完整
- 检查各个日志方法的参数传递
- 验证上下文信息的获取逻辑

## 预期效果

完成测试后，应该能够看到一个完整的电影标签筛选流程日志链，从用户点击标签开始，到最终展示筛选结果结束，每个关键步骤都有清晰的日志记录，并且所有日志都使用统一的VOD_FLOW ID进行关联。

这样的日志跟踪系统将大大提高问题诊断和用户行为分析的效率。
